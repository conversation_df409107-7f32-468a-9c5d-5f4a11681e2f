<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 运维助手</title>
    <link id="theme-css" rel="stylesheet" href="./styles/theme-light.css" />
    <link rel="stylesheet" href="./styles/base.css" />
    <style>
        /* AI运维助手专用样式 */
        body { margin: 0; padding: 0; height: 100vh; overflow: hidden; }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg);
        }
        
        .chat-header {
            background: var(--card);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-soft);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .chat-header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--text);
        }
        
        .chat-status {
            font-size: 12px;
            color: var(--muted);
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .message {
            display: flex;
            gap: 12px;
            animation: slideIn 0.3s ease-out;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .message.ai .message-avatar {
            background: var(--brand-gradient);
            color: var(--brand-foreground);
        }
        
        .message.user .message-avatar {
            background: var(--accent);
            color: white;
        }
        
        .message-content {
            max-width: 70%;
            background: var(--card);
            padding: 12px 16px;
            border-radius: 16px;
            box-shadow: 0 1px 3px var(--shadow-strong);
        }
        
        .message.user .message-content {
            background: var(--accent);
            color: white;
        }
        
        .quick-actions {
            padding: 12px 20px;
            border-bottom: 1px solid var(--border-soft);
            background: var(--card);
        }
        
        .quick-actions-title {
            font-size: 12px;
            color: var(--muted);
            margin-bottom: 8px;
        }
        
        .quick-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .quick-btn {
            background: var(--glass);
            border: 1px solid var(--border-soft);
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 13px;
            color: var(--text);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .quick-btn:hover {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }
        
        .chat-input-area {
            padding: 20px;
            background: var(--card);
            border-top: 1px solid var(--border-soft);
        }
        
        .input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 1px solid var(--border-soft);
            border-radius: 22px;
            background: var(--glass);
            color: var(--text);
            resize: none;
            font-size: 14px;
            line-height: 1.4;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .input-field:focus {
            border-color: var(--accent);
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: var(--accent);
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        
        .send-btn:hover {
            transform: scale(1.05);
        }
        
        .send-btn:disabled {
            background: var(--muted);
            cursor: not-allowed;
            transform: none;
        }
        
        .typing-indicator {
            display: flex;
            gap: 4px;
            padding: 8px 0;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--muted);
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .chat-container { max-width: 100%; }
            .message-content { max-width: 85%; }
            .quick-buttons { gap: 6px; }
            .quick-btn { font-size: 12px; padding: 4px 8px; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 头部 -->
        <div class="chat-header">
            <h1>🤖 AI 运维助手</h1>
            <div class="chat-status">
                <span class="status-dot"></span>
                <span id="status-text">在线</span>
            </div>
        </div>
        
        <!-- 快捷问题按钮 -->
        <div class="quick-actions">
            <div class="quick-actions-title">常见问题 💡</div>
            <div class="quick-buttons">
                <button class="quick-btn" data-question="二维码扫描不出来怎么办？">🔍 二维码问题</button>
                <button class="quick-btn" data-question="无法入库，系统提示找不到订单">📦 入库问题</button>
                <button class="quick-btn" data-question="出库时提示库存冻结怎么处理？">📤 出库问题</button>
                <button class="quick-btn" data-question="系统报错提示账期不对">📅 账期问题</button>
                <button class="quick-btn" data-question="库存显示不适合移动">🚫 移动限制</button>
                <button class="quick-btn" data-question="需要提前入库过账怎么操作？">⏰ 提前过账</button>
            </div>
        </div>
        
        <!-- 消息区域 -->
        <div class="chat-messages" id="chat-messages">
            <!-- 消息会动态添加到这里 -->
        </div>
        
        <!-- 输入区域 -->
        <div class="chat-input-area">
            <div class="input-wrapper">
                <textarea 
                    id="user-input" 
                    class="input-field" 
                    placeholder="描述您遇到的问题，我来帮您解决..." 
                    rows="1"
                ></textarea>
                <button id="send-btn" class="send-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script src="./ai.js"></script>
    <script>
        // 初始化 AI 配置
        (async () => {
            try {
                await AI.loadConfig('./ai-config.json');
                console.log('[AI] 运维助手配置加载成功');
            } catch (e) {
                // 使用内置配置兜底
                await AI.loadConfig({
                    mode: 'direct',
                    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
                    apiKey: 'f1510fc9-df05-4cc7-b21f-85f7249800b6',
                    model: 'doubao-seed-1-6-flash-250715',
                    timeout: 30000,
                    headers: {}
                });
                console.log('[AI] 使用内置配置');
            }
        })();

        // DOM 元素
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const statusText = document.getElementById('status-text');
        const quickButtons = document.querySelectorAll('.quick-btn');

        // 运维知识库系统提示词
        const SYSTEM_PROMPT = `你是一个专业的 WMS (仓库管理系统) 运维助手。你的主要职责是帮助用户解决仓库操作中的各种问题。

请根据以下知识库回答用户问题：

## 二维码相关问题
1. **车身无码**: 在系统中手动输入车身铭牌上的序列号
2. **二维码与铭牌不一致**: 以铭牌序列号为准手动输入，后续在OA系统提交"电子码补码修正"申请
3. **需要提前入库过账**: 联系上游供应商，在"直接交货确认"环节走"港口交货"流程

## 入库问题
1. **查询不到入库订单**: 检查是否属于"港口交货"，如果是则在LTC系统完成收货
2. **过账异常，提示非账期月**: 联系公司财务部门处理

## 出库问题
1. **S4状态为"冻结"**: 先完成该物料的"收货入库"动作，或检查并解除冻结状态
2. **提示库存短缺**: 同上，检查库存状态和入库情况
3. **账期错误**: 修改出库凭证的OB日期，使其与当前账期匹配
4. **库存不适合移动**: 
   - 入库环节报错：联系 MM (物料管理) 模块顾问
   - 出库环节报错：联系 SD (销售与分销) 模块顾问

请用简洁、专业的语言回答，提供具体的操作步骤。如果问题超出知识库范围，建议联系相关技术支持。`;

        // 消息类型
        function addMessage(content, type = 'ai', isTyping = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'ai' ? '🤖' : '👤';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (isTyping) {
                contentDiv.innerHTML = `
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;
            } else {
                contentDiv.innerHTML = content;
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return messageDiv;
        }

        // 发送消息到 AI
        async function sendToAI(userMessage) {
            try {
                statusText.textContent = '思考中...';
                
                const response = await AI.chatCompletion({
                    messages: [
                        { role: 'system', content: SYSTEM_PROMPT },
                        { role: 'user', content: userMessage }
                    ],
                    temperature: 0.3  // 降低随机性，提高回答的准确性
                });
                
                statusText.textContent = '在线';
                
                if (response.choices && response.choices[0] && response.choices[0].message) {
                    return response.choices[0].message.content;
                } else {
                    throw new Error('AI响应格式异常');
                }
            } catch (error) {
                statusText.textContent = '连接异常';
                console.error('AI调用失败:', error);
                
                // 降级到本地知识库
                return getFallbackResponse(userMessage);
            }
        }

        // 本地知识库兜底
        function getFallbackResponse(message) {
            const msg = message.toLowerCase();
            
            if (msg.includes('二维码') || msg.includes('扫码') || msg.includes('无码')) {
                return `关于二维码问题，我为您提供以下解决方案：

**车身无码**
- 在系统中手动输入车身铭牌上的序列号完成操作

**二维码与铭牌不一致**  
- 当前操作以铭牌序列号为准，手动输入完成
- 后续在OA系统提交"电子码补码修正"申请

需要更详细的帮助吗？`;
            }
            
            if (msg.includes('入库') && (msg.includes('订单') || msg.includes('找不到'))) {
                return `入库订单问题解决方案：

1. **查询不到入库订单**
   - 首先检查此订单是否属于"港口交货"
   - 如果是，则无需在此处收货，请在LTC系统完成收货

2. **过账异常，提示非账期月**
   - 请联系公司财务部门进行处理

您遇到的是哪种情况？`;
            }
            
            if (msg.includes('出库') && (msg.includes('冻结') || msg.includes('库存'))) {
                return `出库相关问题解决方案：

**库存冻结/短缺**
- 请仓管员先在系统中完成该物料的"收货入库"动作
- 或检查并解除冻结状态

**账期错误**
- 修改出库凭证的OB日期，使其与当前账期匹配

**库存不适合移动**
- 入库环节报错：联系 MM (物料管理) 模块顾问
- 出库环节报错：联系 SD (销售与分销) 模块顾问

需要我详细说明哪个步骤？`;
            }
            
            return `抱歉，我暂时无法处理这个问题。建议您：

1. 尝试重新描述问题，包含更多关键词
2. 使用上方的快捷问题按钮
3. 联系技术支持获得帮助

有什么其他我可以协助的吗？`;
        }

        // 处理发送消息
        async function handleSend() {
            const message = userInput.value.trim();
            if (!message) return;
            
            // 显示用户消息
            addMessage(message, 'user');
            userInput.value = '';
            sendBtn.disabled = true;
            
            // 显示输入指示器
            const typingMsg = addMessage('', 'ai', true);
            
            // 获取AI回复
            const aiResponse = await sendToAI(message);
            
            // 移除输入指示器，显示AI回复
            chatMessages.removeChild(typingMsg);
            addMessage(aiResponse, 'ai');
            
            sendBtn.disabled = false;
            userInput.focus();
        }

        // 事件监听
        sendBtn.addEventListener('click', handleSend);
        
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSend();
            }
        });

        // 自动调整输入框高度
        userInput.addEventListener('input', () => {
            userInput.style.height = 'auto';
            userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
        });

        // 快捷问题按钮
        quickButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const question = btn.dataset.question;
                userInput.value = question;
                handleSend();
            });
        });

        // 初始化欢迎消息
        setTimeout(() => {
            addMessage(`👋 您好！我是AI运维助手，专门帮助解决WMS仓库管理系统的各种问题。

🔹 您可以直接描述遇到的问题
🔹 或点击上方的快捷问题按钮
🔹 我会为您提供专业的解决方案

请问有什么可以帮助您的？`, 'ai');
        }, 500);
    </script>
</body>
</html>
    <style>
        /* 自定义滚动条和基础样式 */
        html, body {
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }
        .chat-window::-webkit-scrollbar {
            width: 6px;
        }
        .chat-window::-webkit-scrollbar-thumb {
            background-color: #a0aec0;
            border-radius: 3px;
        }
        .chat-window::-webkit-scrollbar-track {
            background-color: #edf2f7;
        }
        /* 解决移动端底部输入框被虚拟键盘遮挡的问题 */
        #app-container {
            height: 100vh;
            height: -webkit-fill-available; /* for Safari */
        }
    </style>
</head>
<body class="bg-gray-100">

    <div id="app-container" class="max-w-3xl mx-auto flex flex-col h-full bg-white shadow-lg">
        <!-- 头部 -->
        <header class="bg-blue-600 text-white p-4 text-center shadow-md z-10">
            <h1 class="text-xl font-bold">AI 运维助手</h1>
        </header>

        <!-- 对话窗口 -->
        <main id="chat-window" class="flex-1 p-4 overflow-y-auto space-y-4">
            <!-- 消息会动态添加到这里 -->
        </main>

        <!-- 输入区域 -->
        <footer class="p-4 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center space-x-2">
                <input type="text" id="user-input" placeholder="请输入您的问题..." class="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition" disabled>
                <button id="send-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition disabled:bg-gray-400" disabled>
                    发送
                </button>
            </div>
        </footer>
    </div>

    <script>
        // --- DOM 元素获取 ---
        const chatWindow = document.getElementById('chat-window');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');

        // --- 知识库 (诊断树) ---
        const knowledgeBase = {
            // 起始节点
            'start': {
                message: '您好！我是您的AI运维助手。请问遇到了什么问题？您可以直接点击下方常见问题。',
                options: [
                    { text: '二维码缺少或有问题', next: 'qr_issue' },
                    { text: '无法入库', next: 'unable_to_store' },
                    { text: '无法出库', next: 'unable_to_checkout' },
                    { text: '其他问题', next: 'other_issue' }
                ]
            },
            // 二维码问题分支
            'qr_issue': {
                message: '好的，关于二维码问题，具体是哪种情况呢？',
                options: [
                    { text: '车身无码', next: 'solution_qr_missing' },
                    { text: '二维码和铭牌不一致', next: 'solution_qr_mismatch' },
                    { text: '需要提前入库过账', next: 'solution_qr_pre_stock' }
                ]
            },
            // 无法入库分支
            'unable_to_store': {
                message: '无法入库，请问具体是什么现象？',
                options: [
                    { text: '查询不到入库订单', next: 'solution_store_no_order' },
                    { text: '过账异常，提示非账期月', next: 'solution_store_period_error' },
                ]
            },
            // 无法出库分支
            'unable_to_checkout': {
                message: '无法出库，请问您观察到的具体情况是什么？',
                options: [
                    { text: '系统有明确报错信息', next: 'checkout_error_message' },
                    { text: 'S4状态为“冻结”', next: 'solution_checkout_frozen' },
                    { text: '提示库存短缺', next: 'solution_checkout_frozen' }
                ]
            },
            'checkout_error_message': {
                message: '好的，报错信息中是否包含以下关键词？',
                options: [
                    { text: '包含“账期”', next: 'solution_checkout_period_error' },
                    { text: '包含“不适合移动”', next: 'solution_checkout_not_movable' },
                    { text: '其他错误', next: 'solution_other_issue' }
                ]
            },
            // 其他问题分支
            'other_issue': {
                 message: '关于其他问题，是否符合以下描述？',
                 options: [
                    { text: '库存不适合移动', next: 'solution_checkout_not_movable' },
                    { text: '以上都不是', next: 'solution_contact_human' }
                ]
            },

            // --- 解决方案节点 ---
            'solution_qr_missing': {
                isSolution: true,
                title: '车身无码',
                cause: '设备上未找到可扫描的二维码。',
                steps: ['在系统中手动输入车身铭牌上的序列号以完成当前操作。'],
                verification: '确认手动输入后，业务流程可以继续。'
            },
            'solution_qr_mismatch': {
                isSolution: true,
                title: '二维码与铭牌不一致',
                cause: '物理二维码信息与设备铭牌信息不匹配。',
                steps: [
                    '当前操作先以铭牌序列号为准，手动输入完成。',
                    '后续需要在OA系统提交“电子码补码修正”申请，以生成新的正确电子码。'
                ],
                verification: '确认后续已提交OA申请。'
            },
            'solution_qr_pre_stock': {
                isSolution: true,
                title: '需要提前入库过账',
                cause: '货物尚未实际到达，但流程需要提前完成入库过账。',
                steps: ['联系上游供应商，在“直接交货确认”环节走“港口交货”流程。'],
                verification: '确认上游已按“港口交货”处理。'
            },
            'solution_store_no_order': {
                isSolution: true,
                title: '无入库订单',
                cause: '系统中查询不到与该物料匹配的入库订单。',
                steps: [
                    '首先，请查询此订单是否属于“港口交货”。',
                    '如果是，则无需在此处进行收货操作，请在LTC系统完成收货。'
                ],
                notes: 'GZP系统已有明确提醒。'
            },
            'solution_store_period_error': {
                isSolution: true,
                title: '入库过账账期错误',
                cause: '过账日期不在当前开放的财务账期内。',
                steps: ['请联系公司财务部门进行处理。'],
                notes: 'GZP系统已有明确提醒。'
            },
            'solution_checkout_period_error': {
                isSolution: true,
                title: '出库凭证账期错误',
                cause: '出库凭证的过账日期（OB Date）不在当前有效的财务账期内。',
                steps: [
                    '需要修改出库凭证的OB日期，使其与当前账期匹配。',
                    '重新扫描并尝试过账出库。'
                ],
                verification: '操作完成后，请您重新扫描出库，检查是否可以正常过账。',
                notes: 'GZP系统已有明确提醒。'
            },
            'solution_checkout_frozen': {
                isSolution: true,
                title: '库存未就绪或状态异常',
                cause: '物料未完成收货入库，或S4系统中的状态为“冻结”、“限制”等，导致库存不可用。',
                steps: ['请仓管员先在系统中完成该物料的“收货入库”动作，或检查并解除冻结状态。'],
                verification: '在系统中查询该物料库存状态，确认已入库/解冻后再进行出库操作。'
            },
            'solution_checkout_not_movable': {
                isSolution: true,
                title: '库存不适合移动',
                cause: '报错信息中明确提示“库存不适合移动”。',
                steps: [
                    '如果是在 **入库** 环节报错，请联系 **MM (物料管理)** 模块顾问处理。',
                    '如果是在 **出库** 环节报错，请联系 **SD (销售与分销)** 模块顾问处理。'
                ],
                verification: '联系相应顾问解除状态后，再尝试操作。'
            },
            'solution_contact_human': {
                isSolution: true,
                title: '需要人工支持',
                cause: '您的问题超出了我的知识范围。',
                steps: ['请联系您的主管或IT运维专家获得进一步帮助。'],
            }
        };

        // --- 核心功能函数 ---

        // 滚动到底部
        function scrollToBottom() {
            chatWindow.scrollTop = chatWindow.scrollHeight;
        }

        // 添加消息到窗口
        function addMessage(text, sender = 'ai') {
            const messageWrapper = document.createElement('div');
            messageWrapper.className = `flex ${sender === 'user' ? 'justify-end' : 'justify-start'}`;
            
            const messageBubble = document.createElement('div');
            messageBubble.className = `max-w-xs md:max-w-md lg:max-w-lg p-3 rounded-lg shadow ${sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}`;
            messageBubble.innerText = text;

            messageWrapper.appendChild(messageBubble);
            chatWindow.appendChild(messageWrapper);
            scrollToBottom();
        }

        // 添加选项按钮
        function addOptions(options) {
            const optionsContainer = document.createElement('div');
            optionsContainer.className = 'flex flex-wrap gap-2 justify-start py-2';

            options.forEach(option => {
                const button = document.createElement('button');
                button.className = 'bg-white border border-blue-500 text-blue-500 px-4 py-2 rounded-full hover:bg-blue-100 transition text-sm';
                button.innerText = option.text;
                button.dataset.next = option.next;
                optionsContainer.appendChild(button);
            });

            chatWindow.appendChild(optionsContainer);
            scrollToBottom();
        }

        // 显示解决方案卡片
        function showSolution(solution) {
            const card = document.createElement('div');
            card.className = 'bg-white border border-green-400 rounded-lg shadow-lg p-4 my-2';
            
            let content = `<h3 class="text-lg font-bold text-green-700 mb-2">${solution.title}</h3>`;
            
            if (solution.cause) {
                content += `<div class="mb-3"><strong class="font-semibold text-gray-700">可能原因：</strong><p class="text-gray-600">${solution.cause}</p></div>`;
            }
            
            if (solution.steps && solution.steps.length > 0) {
                content += `<div class="mb-3"><strong class="font-semibold text-gray-700">处理步骤：</strong><ol class="list-decimal list-inside text-gray-600 mt-1 space-y-1">`;
                solution.steps.forEach(step => {
                    content += `<li>${step}</li>`;
                });
                content += `</ol></div>`;
            }

            if (solution.verification) {
                content += `<div class="mb-3"><strong class="font-semibold text-gray-700">验证方法：</strong><p class="text-gray-600">${solution.verification}</p></div>`;
            }

            if (solution.notes) {
                content += `<div class="mt-3 p-2 bg-yellow-100 border-l-4 border-yellow-400 text-yellow-800 text-sm rounded-r-lg"><p><strong>提醒：</strong> ${solution.notes}</p></div>`;
            }

            // 反馈按钮
            content += `
                <div class="mt-4 pt-3 border-t text-center">
                    <p class="text-sm text-gray-600 mb-2">这个方案是否解决了您的问题？</p>
                    <div class="flex justify-center gap-4">
                        <button onclick="handleFeedback(true)" class="text-green-600 hover:text-green-800 transition">👍 已解决</button>
                        <button onclick="handleFeedback(false)" class="text-red-600 hover:text-red-800 transition">👎 未解决，联系人工</button>
                    </div>
                </div>
            `;

            card.innerHTML = content;
            chatWindow.appendChild(card);
            scrollToBottom();
        }
        
        // 处理用户选择
        function handleUserChoice(nextNodeKey, userText) {
            // 显示用户的选择
            addMessage(userText, 'user');

            // 禁用所有旧按钮
            const allButtons = chatWindow.querySelectorAll('button[data-next]');
            allButtons.forEach(btn => {
                btn.disabled = true;
                btn.className = 'bg-gray-200 border border-gray-300 text-gray-500 px-4 py-2 rounded-full text-sm';
            });

            const node = knowledgeBase[nextNodeKey];
            
            setTimeout(() => {
                if (node) {
                    if (node.isSolution) {
                        showSolution(node);
                    } else {
                        if(node.message) addMessage(node.message);
                        if(node.options) addOptions(node.options);
                    }
                }
            }, 500); // 模拟AI思考
        }

        // 处理反馈
        window.handleFeedback = function(isResolved) {
            if (isResolved) {
                addMessage('太棒了！很高兴能帮到您。如果您有其他问题，可以点击下方按钮重新开始。');
            } else {
                handleUserChoice('solution_contact_human', '未解决，需要人工支持');
            }
             setTimeout(() => addOptions([{ text: '重新开始', next: 'start' }]), 500);
        }

        // --- 事件监听 ---
        chatWindow.addEventListener('click', (e) => {
            if (e.target.tagName === 'BUTTON' && e.target.dataset.next) {
                const nextNodeKey = e.target.dataset.next;
                const userText = e.target.innerText;
                handleUserChoice(nextNodeKey, userText);
            }
        });

        // --- 初始化 ---
        function init() {
            const startNode = knowledgeBase['start'];
            setTimeout(() => {
                addMessage(startNode.message);
                addOptions(startNode.options);
            }, 500);
        }

        init();

    </script>
</body>
</html>

# AI WMS 操作台（原型）

一个零依赖、纯 HTML/CSS/JS 的交互原型，用于演示“自然语言驱动 + 可视确认 + 可审计”的仓储作业流（WMS）体验。

## 功能概览
- 命令输入（顶部命令框或底部输入框）
- 极简意图解析（拣货/出库、收货/入库、退货、流程编排）
- 动作卡（Action Card）预览：意图、置信度、JSON 载荷、影响预估
- Dry-Run 切换：不改动真实库存，仅模拟执行
- 库存数模拟展示与自动变更（出库 -，入库/退货 +）
- 流程模板：保存、应用、删除；流程编排弹窗（可编辑草案）
- 审计日志：记录每一次 AI 解析与执行

## 运行方式
无需安装依赖，直接双击或用浏览器打开 `demo.html`。

Windows PowerShell 下也可以用：

```powershell
Start-Process msedge "$(Resolve-Path .\demo.html)"
```

或替换为你常用的浏览器（chrome、firefox 等）。

## 目录结构
- `test.html` — 独立的 AI 能力测试台（加载配置、文本对话、多模态、mock 解析）
- `README.md` — 本说明文件

## 关键交互点
- 顶栏命令框：输入后回车；Ctrl/Cmd + K 聚焦到底部输入

同理，你也可以打开 `test.html` 自助验证 AI 直连、mock、多模态等能力；若需要从 JSON 读取配置（`ai-config.json`），建议通过本地服务器访问以避免浏览器的 file:// 限制。
- Dry-Run：默认 ON；点击切换
- 动作卡：确认执行或修改（打开流程编排器）
- 侧栏：检索 SOP、流程模板的保存/应用/删除、审计日志
- 快捷操作：开始拣货、开始收货、打开模板中心

## 代码说明
- `mockLLMParse(text)`：模拟意图解析，产出 `{ intent, confidence, payload, actionHtml }`
- `confirmAction(intent)`：执行业务模拟；根据意图决定库存变更的正负号
- `editAction()`：打开流程编排器并填充示例草案
- `renderFlows()`/`applyFlow()`/`deleteFlow()`：流程模板管理
- `renderAudit()`：审计面板渲染

本原型不含后端，所有数据均存储在运行期内存中，页面刷新会重置。

## 在其他页面复用通用 AI 服务
在你的 HTML 中引入模块：

```html
<script src="./ai.js"></script>
<script>
	// 1) 模拟模式（前端无外网/无密钥时用于演示）
	AI.configure({ mode: 'mock' });

	// 轻量意图解析（与 demo 的 mock 行为一致）
	const parsed = AI.mockParse('出库给客户A 500件，优先近效期');
	console.log(parsed.intent, parsed.confidence, parsed.payload);

	// 2) 直连 Ark（不推荐在生产中暴露前端密钥，建议使用后端代理）
	AI.configure({
		mode: 'direct',
		baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
		apiKey: 'YOUR_ARK_API_KEY', // 仅示例，生产请改代理
		model: 'ep-xxxx',
		timeout: 30000,
	});

	// 文本对话
	const res = await AI.chatCompletion({
		messages: [{ role: 'user', content: '这是一个测试' }],
	});
	console.log(res.choices?.[0]);

	// 多模态（图像 + 文本）
	const vision = await AI.visionDescribe({
		imageUrl: 'https://example.com/image.jpg',
		question: '这是哪里？',
	});
	console.log(vision.choices?.[0]);
</script>
```

### 使用 JSON 配置文件（推荐便于切换环境）
在仓库根目录新增 `ai-config.json`（本仓库已提供示例），字段：

```json
{
	"mode": "direct",
	"baseURL": "https://ark.cn-beijing.volces.com/api/v3",
	"apiKey": "REPLACE_WITH_ARK_API_KEY",
	"model": "REPLACE_WITH_MODEL_ID",
	"timeout": 30000,
	"headers": {}
}
```

页面中加载：

```html
<script src="./ai.js"></script>
<script>
	await AI.loadConfig('./ai-config.json');
	// 之后直接调用 AI.chatCompletion / AI.visionDescribe
</script>
```

注意：如果你用文件方式直接打开 HTML（file://），浏览器通常禁止跨源读取本地 JSON。建议:
- 启动一个本地静态服务器（例如 VS Code 的 Live Server 或任何简易 http server）
- 或者将配置以内联对象 `AI.loadConfig({...})` 的方式传入（不含密钥的场景）

### 后端代理（推荐）
为了安全，建议前端不携带密钥。可在后端实现 `/api/ai/chat` 代理转发到 Ark，然后将 `AI.configure({ mode: 'proxy' })`，并在 `ai.js` 中的 `proxy` 分支填入 `fetch('/api/ai/chat')` 的调用（本仓库默认未启用）。

## 后续可拓展
- 接入真实 LLM 与向量检索（解析意图 + SOP 检索）
- 用户与角色/权限体系、审批流
- 任务/波次/设备联动与实时状态
- 持久化存储与后端 API（审计、流程、库存）
- 单元测试与可观测性（埋点、操作回放）
